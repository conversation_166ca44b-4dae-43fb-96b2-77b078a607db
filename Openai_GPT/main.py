from fastapi import FastAPI
from pydantic import BaseModel
from transformers import pipeline
import torch

app = FastAPI()

class PromptRequest(BaseModel):
    prompt: str


device = 0 if torch.cuda.is_available() else -1
generator = pipeline(
    "text-generation",
    model="microsoft/DialoGPT-medium",
    device=device,
    max_length=512,
    do_sample=True,
    temperature=0.7
)

@app.post("/generate")
def generate(request: PromptRequest):
    try:
        result = generator(
            request.prompt,
            max_new_tokens=200,
            num_return_sequences=1,
            pad_token_id=generator.tokenizer.eos_token_id
        )
        response_text = result[0]['generated_text']
        if response_text.startswith(request.prompt):
            response_text = response_text[len(request.prompt):].strip()
        return {"response": response_text}
    except Exception as e:
        return {"error": str(e)}
