from fastapi import FastAPI
from pydantic import BaseModel
from langchain.llms import Ollama
from typing import Optional

app = FastAPI()

class PromptRequest(BaseModel):
    prompt: str
    temperature: Optional[float] = None

llm = Ollama(model="gpt-oss:20b")

@app.post("/generate")
def generate(request: PromptRequest):
    try:
        llm_params = {}
        if request.temperature is not None:
            llm_params['temperature'] = request.temperature
        response = llm(request.prompt, **llm_params)
        return {"response": response}
    except Exception as e:
        return {"error": str(e)}
