from fastapi import FastAPI
from pydantic import BaseModel
from langchain.llms import Ollama
from typing import Optional

app = FastAPI()

class PromptRequest(BaseModel):
    prompt: str

llm = Ollama(model="gpt-oss:20b")

@app.post("/generate")
def generate(request: PromptRequest):
    try:
        response = llm(request.prompt)
        return {"response": response}
    except Exception as e:
        return {"error": str(e)}
