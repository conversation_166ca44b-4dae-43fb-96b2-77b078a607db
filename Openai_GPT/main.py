from fastapi import FastAP<PERSON>
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from transformers import pipeline
import torch

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with your domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class PromptRequest(BaseModel):
    prompt: str

# Initialize Hugging Face pipeline for CPU only
generator = pipeline(
    "text-generation",
    model="distilgpt2",  # Lightweight model, perfect for CPU
    device=-1,  # Force CPU usage
    torch_dtype=torch.float32,  # Better for CPU
    model_kwargs={"low_cpu_mem_usage": True}
)

@app.get("/")
def read_root():
    return FileResponse('index.html')

@app.post("/generate")
def generate(request: PromptRequest):
    try:
        # Generate response with CPU-optimized settings
        result = generator(
            request.prompt,
            max_new_tokens=150,  # Reduced for faster CPU processing
            num_return_sequences=1,
            do_sample=True,
            temperature=0.7,
            pad_token_id=generator.tokenizer.eos_token_id,
            truncation=True
        )

        # Extract the generated text
        response_text = result[0]['generated_text']

        # Remove the original prompt from the response if it's included
        if response_text.startswith(request.prompt):
            response_text = response_text[len(request.prompt):].strip()

        return {"response": response_text}
    except Exception as e:
        return {"error": str(e)}
