from fastapi import FastAPI
from pydantic import BaseModel
from transformers import pipeline
import torch

app = FastAPI()

class PromptRequest(BaseModel):
    prompt: str


generator = pipeline(
    "text-generation",
    model="bartowski/openai_gpt-oss-20b-GGUF",
    device=-1,
    torch_dtype=torch.float32,
    model_kwargs={"low_cpu_mem_usage": True}
)

@app.post("/generate")
def generate(request: PromptRequest):
    try:
        result = generator(
            request.prompt,
            max_new_tokens=150,
            num_return_sequences=1,
            do_sample=True,
            temperature=0.7,
            pad_token_id=generator.tokenizer.eos_token_id,
            truncation=True
        )
        response_text = result[0]['generated_text']
        if response_text.startswith(request.prompt):
            response_text = response_text[len(request.prompt):].strip()
        return {"response": response_text}
    except Exception as e:
        return {"error": str(e)}
