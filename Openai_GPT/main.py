from fastapi import FastAPI
from pydantic import BaseModel
from langchain.llms import Ollama
from typing import Optional

app = FastAPI()

class PromptRequest(BaseModel):
    prompt: str
    system_message: Optional[str] = None
    temperature: Optional[float] = None

llm = Ollama(model="gpt-oss:20b")

def build_full_prompt(prompt: str, system_message: Optional[str] = None) -> str:
    if system_message:
        return f"{system_message}\n\n{prompt}"
    return prompt

@app.post("/generate")
def generate(request: PromptRequest):
    full_prompt = build_full_prompt(request.prompt, request.system_message)
    try:
        # Configure temperature if provided
        llm_params = {}
        if request.temperature is not None:
            llm_params['temperature'] = request.temperature

        response = llm(full_prompt, **llm_params)
        return {
            "response": response,
            "prompt_used": full_prompt
        }
    except Exception as e:
        return {"error": str(e)}

# Keep the original job skills endpoint for backward compatibility
class JobDescriptionRequest(BaseModel):
    job_description: str

@app.post("/extract-skills")
def extract_skills(request: JobDescriptionRequest):
    system_message = (
        "You are an AI assistant. Extract all the required skills from the following job description. "
        "Return the skills as a JSON array of strings, e.g. [\"Python\", \"Machine Learning\", \"Communication Skills\"]."
    )
    full_prompt = f"{system_message}\n\nJob description:\n{request.job_description}"
    try:
        response = llm(full_prompt)
        return {"skills_json": response}
    except Exception as e:
        return {"error": str(e)}
