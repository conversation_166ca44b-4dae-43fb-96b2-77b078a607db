from fastapi import FastAPI
from pydantic import BaseModel
from langchain.llms import Ollama

app = FastAPI()

class PromptRequest(BaseModel):
    job_description: str

llm = Ollama(model="gpt-oss:20b")

def build_prompt(job_description: str) -> str:
    return (
        "You are an AI assistant. Extract all the required skills from the following job description. "
        "Return the skills as a JSON array of strings, e.g. [\"Python\", \"Machine Learning\", \"Communication Skills\"].\n\n"
        f"Job description:\n{job_description}"
    )

@app.post("/generate")
def generate(request: PromptRequest):
    prompt = build_prompt(request.job_description)
    try:
        response = llm(prompt)
        return {"skills_json": response}
    except Exception as e:
        return {"error": str(e)}
